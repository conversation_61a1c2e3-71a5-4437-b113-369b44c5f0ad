import React, { useEffect, useRef, useState } from 'react';
import { useDebounceState } from 'shared/utils/hooks/useDebounceState';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import type { AsyncRenderItemProps } from 'shared/types/components/Form.type';
import AutoComplete from './index';
import useMedia from '../utils/useMedia';

const AsyncRenderItem = ({
  name,
  value: parentValue,
  url,
  normalizer,
  onChange: parentOnchange,
  onChangeInput: parentOnChangeInput,
  keywords = 'text',
  isMulti,
  apiFunc,
  initSearchValue = 'a',
  params = {},
  showDropDownWithoutEnteringAnything,
  onOptions,
  optionsVariant,
  hardRefetch = false,
  accessToken,
  renderItem,
  ...rest
}: AsyncRenderItemProps) => {
  const { isMoreThanTablet } = useMedia();
  const [filteredOption, setFilteredOption] = useState([]);
  const { debounceValue, setValue } = useDebounceState<string>(
    initSearchValue,
    500
  );
  const ref = useRef<any>(null);

  const onSuccess = (data: any) => {
    const newItems = normalizer ? normalizer(data) : data;
    setFilteredOption(newItems);
    onOptions?.(newItems || []);
  };

  const { refetch } = useReactQuery({
    action: {
      spreadParams: !!apiFunc,
      apiFunc,
      key: [name, debounceValue],
      url,
      params: { [keywords]: debounceValue, ...params },
      accessToken,
    },
    config: {
      enabled: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      staleTime: 60 * 60 * 1000,
      onSuccess,
    },
  });

  useEffect(() => {
    if (debounceValue || showDropDownWithoutEnteringAnything || hardRefetch) {
      refetch();
    }
  }, [
    debounceValue,
    refetch,
    showDropDownWithoutEnteringAnything,
    hardRefetch,
  ]);

  const onChangeInput = (input: string) => {
    parentOnChangeInput?.(input);
    setValue(input);
    if (!isMulti) {
      parentOnchange?.({ label: input, value: null });
    }
    if (!input) {
      setValue(initSearchValue);
    }
  };

  const onSelect = (input: string) => parentOnchange?.(input);

  return (
    <AutoComplete
      onChangeInput={onChangeInput}
      onSelect={onSelect}
      ref={ref}
      value={parentValue}
      displayName={parentValue?.label}
      isMulti={isMulti}
      name={name}
      options={
        debounceValue || showDropDownWithoutEnteringAnything
          ? filteredOption
          : []
      }
      optionsVariant={
        optionsVariant || (isMoreThanTablet ? 'dropdown' : 'modal')
      }
      renderItem={renderItem}
      {...rest}
    />
  );
};

export default AsyncRenderItem;
