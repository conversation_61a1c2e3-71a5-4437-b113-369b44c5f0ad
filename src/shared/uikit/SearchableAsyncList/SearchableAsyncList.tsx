import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useDebounceState } from 'shared/utils/hooks/useDebounceState';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import type { AutoCompleteOption } from 'shared/types/components/Form.type';
import AutoComplete from '../AutoComplete';
import useMedia from '../utils/useMedia';

export interface SearchableAsyncListProps<
  T extends AutoCompleteOption = AutoCompleteOption,
> {
  name: string;
  value?: T | T[];
  variant: 'single' | 'multi';
  renderItem: (args: {
    item: T;
    isSelected: boolean;
    index: number;
    text?: string;
  }) => React.ReactNode;

  // Async search functionality
  url?: string;
  apiFunc?: (params: any) => Promise<any>;
  normalizer?: (data: any) => T[];
  keywords?: string;
  params?: Record<string, any>;
  initSearchValue?: string;
  showDropDownWithoutEnteringAnything?: boolean;
  hardRefetch?: boolean;
  accessToken?: string;

  // Event handlers
  onChange?: (value: T | T[] | undefined) => void;
  onChangeInput?: (input: string) => void;
  onOptions?: (options: T[]) => void;

  // UI props
  label?: string;
  placeholder?: string;
  helperText?: string;
  error?: string;
  disabled?: boolean;
  className?: string;
  optionsVariant?: 'dropdown' | 'modal' | 'bottomsheet' | 'none';
  limit?: number;

  // Other AutoComplete props
  [key: string]: any;
}

const SearchableAsyncList = <
  T extends AutoCompleteOption = AutoCompleteOption,
>({
  name,
  value: parentValue,
  variant,
  renderItem,
  url,
  normalizer,
  onChange: parentOnChange,
  onChangeInput: parentOnChangeInput,
  keywords = 'text',
  apiFunc,
  initSearchValue = 'a',
  params = {},
  showDropDownWithoutEnteringAnything,
  onOptions,
  optionsVariant,
  hardRefetch = false,
  accessToken,
  limit = variant === 'multi' ? 10 : 3,
  ...rest
}: SearchableAsyncListProps<T>) => {
  const { isMoreThanTablet } = useMedia();
  const [filteredOptions, setFilteredOptions] = useState<T[]>([]);
  const { debounceValue, setValue } = useDebounceState<string>(
    initSearchValue,
    500
  );
  const ref = useRef<any>(null);

  const onSuccess = useCallback(
    (data: any) => {
      const newItems = normalizer ? normalizer(data) : data;
      setFilteredOptions(newItems);
      onOptions?.(newItems || []);
    },
    [normalizer, onOptions]
  );

  const { refetch } = useReactQuery({
    action: {
      spreadParams: !!apiFunc,
      apiFunc,
      key: [name, debounceValue],
      url,
      params: { [keywords]: debounceValue, ...params },
      accessToken,
    },
    config: {
      enabled: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      staleTime: 60 * 60 * 1000,
      onSuccess,
    },
  });

  useEffect(() => {
    if (debounceValue || showDropDownWithoutEnteringAnything || hardRefetch) {
      refetch();
    }
  }, [
    debounceValue,
    refetch,
    showDropDownWithoutEnteringAnything,
    hardRefetch,
  ]);

  const onChangeInput = useCallback(
    (input: string) => {
      parentOnChangeInput?.(input);
      setValue(input);

      // For single variant, clear selection when typing
      if (variant === 'single') {
        parentOnChange?.({ label: input, value: null } as T);
      }

      if (!input) {
        setValue(initSearchValue);
      }
    },
    [parentOnChangeInput, setValue, variant, parentOnChange, initSearchValue]
  );

  const onSelect = useCallback(
    (selectedItem: T | T[]) => {
      if (variant === 'single') {
        // For single variant, replace the current selection
        parentOnChange?.(selectedItem as T);
      } else {
        // For multi variant, the AutoComplete component handles the array logic
        parentOnChange?.(selectedItem as T[]);
      }
    },
    [variant, parentOnChange]
  );

  // Determine if we're in multi mode for the AutoComplete component
  const isMulti = variant === 'multi';

  // Handle value formatting for AutoComplete
  const autoCompleteValue = isMulti
    ? Array.isArray(parentValue)
      ? parentValue
      : []
    : Array.isArray(parentValue)
      ? parentValue[0]
      : parentValue;

  return (
    <AutoComplete<T, typeof isMulti>
      onChangeInput={onChangeInput}
      onSelect={onSelect}
      ref={ref}
      value={autoCompleteValue}
      displayName={isMulti ? undefined : (autoCompleteValue as T)?.label}
      isMulti={isMulti}
      name={name}
      options={
        debounceValue || showDropDownWithoutEnteringAnything
          ? filteredOptions
          : []
      }
      optionsVariant={
        optionsVariant || (isMoreThanTablet ? 'dropdown' : 'modal')
      }
      renderItem={renderItem}
      limit={limit}
      {...rest}
    />
  );
};

export default SearchableAsyncList;
