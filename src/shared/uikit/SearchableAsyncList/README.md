# SearchableAsyncList Component

A generic, reusable async autocomplete component that supports both single and multi-selection modes with custom item rendering.

## Features

- **Two Selection Modes**: `single` and `multi` variants
- **Async Search**: Built-in debounced search with API integration
- **Custom Rendering**: Generic `renderItem` prop for flexible item display
- **Form Integration**: Works seamlessly with the existing form system
- **Responsive**: Adapts to different screen sizes (dropdown on desktop, modal on mobile)

## Basic Usage

### Single Selection

```tsx
import SearchableAsyncList from 'shared/uikit/SearchableAsyncList';

const MyComponent = () => {
  const [selectedUser, setSelectedUser] = useState();

  const renderUserItem = ({ item, isSelected }) => (
    <div className={`p-2 ${isSelected ? 'bg-blue-100' : ''}`}>
      <div className="font-semibold">{item.label}</div>
      <div className="text-sm text-gray-500">{item.helperText}</div>
    </div>
  );

  return (
    <SearchableAsyncList
      name="user-search"
      variant="single"
      value={selectedUser}
      onChange={setSelectedUser}
      renderItem={renderUserItem}
      apiFunc={searchUsersApi}
      label="Select a user"
      placeholder="Search users..."
    />
  );
};
```

### Multi Selection

```tsx
const MyComponent = () => {
  const [selectedUsers, setSelectedUsers] = useState([]);

  return (
    <SearchableAsyncList
      name="users-search"
      variant="multi"
      value={selectedUsers}
      onChange={setSelectedUsers}
      renderItem={renderUserItem}
      apiFunc={searchUsersApi}
      label="Select users"
      placeholder="Search users..."
      limit={10}
    />
  );
};
```

### With Form Builder

```tsx
// In your form configuration
const formGroups = [
  {
    name: 'assignedUsers',
    cp: 'searchableAsyncList',
    label: 'Assign Users',
    variant: 'multi',
    apiFunc: searchUsersApi,
    renderItem: ({ item, isSelected }) => (
      <UserListItem user={item} selected={isSelected} />
    ),
    required: true,
  }
];
```

## Props

### Required Props

- `name: string` - Unique identifier for the component
- `variant: 'single' | 'multi'` - Selection mode
- `renderItem: (args) => ReactNode` - Function to render individual items

### Async Search Props

- `apiFunc?: (params) => Promise<any>` - API function for fetching data
- `url?: string` - Alternative API endpoint URL
- `normalizer?: (data) => T[]` - Function to normalize API response
- `keywords?: string` - Search parameter key (default: 'text')
- `params?: Record<string, any>` - Additional API parameters
- `initSearchValue?: string` - Initial search value (default: 'a')
- `showDropDownWithoutEnteringAnything?: boolean` - Show options without typing
- `hardRefetch?: boolean` - Force refetch on mount
- `accessToken?: string` - Authentication token

### Event Handlers

- `onChange?: (value) => void` - Called when selection changes
- `onChangeInput?: (input: string) => void` - Called when input changes
- `onOptions?: (options: T[]) => void` - Called when options are loaded

### UI Props

- `label?: string` - Field label
- `placeholder?: string` - Input placeholder
- `helperText?: string` - Helper text below input
- `error?: string` - Error message
- `disabled?: boolean` - Disable the component
- `className?: string` - Additional CSS classes
- `optionsVariant?: 'dropdown' | 'modal' | 'bottomsheet' | 'none'` - Options display mode
- `limit?: number` - Maximum number of selections (multi mode)

## API Function Format

Your API function should accept parameters and return a promise:

```tsx
const searchUsersApi = async ({ text, ...otherParams }) => {
  const response = await fetch(`/api/users/search?q=${text}`);
  return response.json();
};
```

## Data Format

Items should follow the `AutoCompleteOption` interface:

```tsx
interface AutoCompleteOption {
  label: string;
  value: any;
  helperText?: string;
  leftIcon?: ReactNode;
  image?: string;
  disabled?: boolean;
}
```

## Behavior

### Single Variant
- Clears previous selection when new item is selected
- Shows selected item label in input
- Passes single item to `onChange`

### Multi Variant
- Allows multiple selections
- Shows selected items as tags below input
- Passes array of items to `onChange`
- Respects `limit` prop for maximum selections

## Examples

See `SearchableAsyncList.example.tsx` for complete working examples of both variants.
