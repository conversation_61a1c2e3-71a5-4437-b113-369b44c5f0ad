import React, { useState } from 'react';
import SearchableAsyncList from './SearchableAsyncList';
import type { AutoCompleteOption } from 'shared/types/components/Form.type';
import Flex from '../Flex';
import Typography from '../Typography';
import Button from '../Button';

// Mock data for testing
const mockUsers = [
  { label: '<PERSON>', value: '1', helperText: 'Software Engineer' },
  { label: '<PERSON>', value: '2', helperText: 'Product Manager' },
  { label: '<PERSON>', value: '3', helperText: 'Designer' },
  { label: '<PERSON>', value: '4', helperText: 'Data Scientist' },
  { label: '<PERSON>', value: '5', helperText: 'DevOps Engineer' },
];

// Mock API function that simulates async search
const mockApiFunc = async ({ text }: { text: string }) => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // Filter users based on search text
  const filtered = mockUsers.filter(user => 
    user.label.toLowerCase().includes(text.toLowerCase()) ||
    user.helperText.toLowerCase().includes(text.toLowerCase())
  );
  
  return filtered;
};

// Custom render item function
const renderUserItem = ({ item, isSelected }: { 
  item: AutoCompleteOption; 
  isSelected: boolean; 
  index: number; 
  text?: string 
}) => (
  <Flex 
    flexDir="column" 
    className={`p-3 ${isSelected ? 'bg-blue-100' : 'hover:bg-gray-50'}`}
  >
    <Typography size={14} font="600" color={isSelected ? 'primary' : 'smoke_coal'}>
      {item.label}
    </Typography>
    {item.helperText && (
      <Typography size={12} color="secondaryDisabledText">
        {item.helperText}
      </Typography>
    )}
  </Flex>
);

const SearchableAsyncListExample: React.FC = () => {
  const [singleValue, setSingleValue] = useState<AutoCompleteOption | undefined>();
  const [multiValue, setMultiValue] = useState<AutoCompleteOption[]>([]);

  const handleSingleChange = (value: AutoCompleteOption | undefined) => {
    console.log('Single selection changed:', value);
    setSingleValue(value);
  };

  const handleMultiChange = (value: AutoCompleteOption[] | undefined) => {
    console.log('Multi selection changed:', value);
    setMultiValue(value || []);
  };

  const clearSelections = () => {
    setSingleValue(undefined);
    setMultiValue([]);
  };

  return (
    <Flex flexDir="column" className="p-6 space-y-6 max-w-2xl">
      <Typography size={24} font="700" color="smoke_coal">
        SearchableAsyncList Examples
      </Typography>

      {/* Single Selection Example */}
      <Flex flexDir="column" className="space-y-2">
        <Typography size={18} font="600" color="smoke_coal">
          Single Selection Variant
        </Typography>
        <SearchableAsyncList
          name="single-user-search"
          variant="single"
          value={singleValue}
          onChange={handleSingleChange}
          renderItem={renderUserItem}
          apiFunc={mockApiFunc}
          keywords="text"
          label="Select a user"
          placeholder="Search for users..."
          helperText="Type to search for users by name or role"
          initSearchValue=""
          showDropDownWithoutEnteringAnything={false}
        />
        {singleValue && (
          <Typography size={12} color="secondaryText">
            Selected: {singleValue.label} ({singleValue.helperText})
          </Typography>
        )}
      </Flex>

      {/* Multi Selection Example */}
      <Flex flexDir="column" className="space-y-2">
        <Typography size={18} font="600" color="smoke_coal">
          Multi Selection Variant
        </Typography>
        <SearchableAsyncList
          name="multi-user-search"
          variant="multi"
          value={multiValue}
          onChange={handleMultiChange}
          renderItem={renderUserItem}
          apiFunc={mockApiFunc}
          keywords="text"
          label="Select multiple users"
          placeholder="Search for users..."
          helperText="Type to search and select multiple users"
          initSearchValue=""
          showDropDownWithoutEnteringAnything={false}
          limit={10}
        />
        {multiValue.length > 0 && (
          <Flex flexDir="column" className="space-y-1">
            <Typography size={12} color="secondaryText">
              Selected ({multiValue.length}):
            </Typography>
            {multiValue.map((user, index) => (
              <Typography key={user.value} size={11} color="secondaryText">
                {index + 1}. {user.label} ({user.helperText})
              </Typography>
            ))}
          </Flex>
        )}
      </Flex>

      {/* Clear button */}
      <Button
        variant="outline"
        schema="secondary"
        label="Clear All Selections"
        onClick={clearSelections}
        className="w-fit"
      />

      {/* Debug info */}
      <Flex flexDir="column" className="space-y-2 p-4 bg-gray-50 rounded">
        <Typography size={14} font="600" color="smoke_coal">
          Debug Info:
        </Typography>
        <Typography size={12} color="secondaryText">
          Single Value: {JSON.stringify(singleValue, null, 2)}
        </Typography>
        <Typography size={12} color="secondaryText">
          Multi Value: {JSON.stringify(multiValue, null, 2)}
        </Typography>
      </Flex>
    </Flex>
  );
};

export default SearchableAsyncListExample;
