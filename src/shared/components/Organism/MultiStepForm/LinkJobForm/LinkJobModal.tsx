import type { FC } from 'react';
import React, { useMemo } from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import formValidator from 'shared/utils/form/formValidator';
import { addJobsToProject } from 'shared/utils/api/project';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from 'shared/hooks/useMultiStepForm';
import useToast from 'shared/uikit/Toast/useToast';
import { linkJobsToCandidate } from '@shared/utils/api/candidates';
import { useQueryClient } from '@tanstack/react-query';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import {
  addCandidate,
  batchAddCandidatesToJobs,
  getCandidacyLinkedJobs,
} from '@shared/utils/api/jobs';
import type { IJobApi } from '@shared/types/job';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import MultiStepForm from '../MultiStepForm';
import type { MultiStepFormProps } from '../MultiStepForm';
import useLinkJobForm from './useLinkJobForm';
import classes from './LinkJobModal.module.scss';

interface LinkJobModalProps {
  data?: {
    id: string;
    target: 'project' | 'candidate' | 'job';
    initialJobs: Array<{ id: string }>;
  };
}

const LinkJobModal: FC<LinkJobModalProps> = ({ data }) => {
  const queryClient = useQueryClient();
  const target = data?.target ?? '';
  const targetId = data?.id ?? '';
  const { t } = useTranslation();
  const { options } = useMultiStepFormState('linkJobForm');
  const onClose = () => closeMultiStepForm('linkJobForm');
  const formData = useLinkJobForm(data?.target ?? 'project') as any[];
  const totalSteps = useMemo(() => formData.length ?? 0, [formData]);
  const toast = useToast();
  const getHeaderProps = getStepData('getHeaderProps', formData);
  const getStepHeaderProps = getStepData('getStepHeaderProps', formData);
  const renderFooter = getStepData('renderFooter', formData);
  const renderBody = getStepData('renderBody', formData);
  const { handleChangeParams } = useCustomParams();

  const linkedJobsQuery = useReactQuery<IJobApi[]>({
    action: {
      apiFunc: () => getCandidacyLinkedJobs(targetId || ''),

      key: [QueryKeys.getCandidateRecruiterJobsList, targetId],
    },
    config: {
      enabled: target === 'candidate' && Boolean(targetId),
      refetchOnWindowFocus: false,
    },
  });

  const initialValues = useMemo(
    () => ({
      jobs: data?.initialJobs ?? linkedJobsQuery.data ?? [],
    }),
    [data?.initialJobs, linkedJobsQuery.data]
  );

  const apiFunc: MultiStepFormProps['apiFunc'] = async ({
    jobs,
  }: {
    jobs: any[];
  }) => {
    const jobIds = jobs?.map((job) => job.id);
    if (target === 'project') {
      await addJobsToProject(targetId, jobIds);
    } else if (target === 'candidate') {
      await linkJobsToCandidate(targetId, jobIds);
      Promise.all([
        queryClient.invalidateQueries({
          queryKey: [QueryKeys.getCandidateActivities, targetId],
          exact: true,
        }),
        queryClient.invalidateQueries({
          queryKey: [QueryKeys.getCandidateRecruiterJobsList, targetId],
          exact: true,
        }),
      ]);
    } else {
      if (typeof targetId === 'string') {
        await addCandidate({ jobId: targetId, candidateIds: jobIds });
      } else {
        await batchAddCandidatesToJobs({
          jobIds: targetId,
          candidateIds: jobIds,
        });
      }
      Promise.all([
        queryClient.invalidateQueries({
          queryKey: [QueryKeys.jobCandidates, Number(targetId), '0'],
        }),
        queryClient.invalidateQueries({
          queryKey: [QueryKeys.jobDetails, String(targetId)],
        }),
      ]);
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: [QueryKeys.getPipelinesList],
          exact: false,
        });
      }, 2000);
      handleChangeParams({
        add: { refresh: 'true' },
      });
      options?.handleRefetch?.();
    }
  };

  const onAlert = async (message: string, type: 'success' | 'error') => {
    toast({
      type,
      icon: `${type === 'success' ? 'check' : 'times'}-circle`,
      title: target === 'project' ? t('job_linked') : t('candidate_submitted'),
      message,
    });
    onClose();
  };

  const getValidationSchema: MultiStepFormProps['getValidationSchema'] =
    React.useCallback(({ step }: { step: number }) => {
      const validationSchema = {
        1: {
          jobs: formValidator.array().min(1),
        },
      } as any;

      return formValidator.object().shape(validationSchema[step]);
    }, []);

  const onSuccess = () => {
    if (target === 'project') {
      onAlert(t('job_linked_successfully'), 'success');
    } else if (target === 'candidate') {
      onAlert(t('your_candidate_submitted_success'), 'success');
    } else {
      onAlert(t('candidates_linked_successfully'), 'success');
    }
  };

  return (
    <MultiStepForm
      apiFunc={apiFunc}
      getValidationSchema={getValidationSchema}
      totalSteps={totalSteps}
      initialValues={initialValues}
      getHeaderProps={getHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      getStepHeaderProps={getStepHeaderProps}
      onClose={onClose}
      onSuccess={onSuccess}
      onFailure={(error) =>
        onAlert(error.message ?? error.defaultMessage ?? 'Error!', 'error')
      }
      enableReinitialize
      formName="createProjectForm"
      isOpenAnimation
      backdropClassName={classes.backdrop}
    />
  );
};

export default LinkJobModal;

function getStepData<T extends keyof MultiStepFormProps>(
  key: T,
  data: MultiStepFormProps[]
): MultiStepFormProps[T] {
  return ({ step, ...rest }: any) => data[step][key]?.({ step, ...rest });
}
