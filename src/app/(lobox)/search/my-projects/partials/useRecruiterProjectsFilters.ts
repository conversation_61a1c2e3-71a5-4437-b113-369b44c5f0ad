'use client';

import { searchFilterQueryParams } from 'shared/constants/search';
import { useSearchParams } from 'next/navigation';

const useRecruiterProjectsFilters = () => {
  const searchParams = useSearchParams();

  const status = searchParams.get(searchFilterQueryParams.status);
  const ownerIds = searchParams.get(searchFilterQueryParams.ownerIds);
  const collaboratorUserIds = searchParams.get(
    searchFilterQueryParams.collaboratorUserIds
  );
  const creatorIds = searchParams.get(searchFilterQueryParams.creatorIds);
  const jobIds = searchParams.get(searchFilterQueryParams.jobIds);
  const tags = searchParams.get(searchFilterQueryParams.tags);
  const dateRangeType =
    searchParams.get(searchFilterQueryParams.dateRangeType) ?? 'ANY_TIME';
  const sortBy =
    searchParams.get(searchFilterQueryParams.sortBy) ?? 'MOST_RECENT';
  const page =
    searchParams.get(searchFilterQueryParams.page) ?? ('0' as string);
  const currentEntityId = searchParams.get('currentEntityId');
  const shouldRefresh = searchParams.get('refresh') ?? 'false';
  const text = searchParams.get('query') ?? 'false';

  const filters = {
    text,
    status,
    creatorIds,
    ownerIds,
    jobIds,
    dateRangeType,
    page,
    sortBy,
    collaboratorUserIds,
    tags,
    size: 10,
  };

  return { filters, currentEntityId, shouldRefresh };
};

export default useRecruiterProjectsFilters;
